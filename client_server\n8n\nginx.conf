server {
    listen 80;
    server_name devn8n.altios.ai;
    return 301 https://$host$request_uri;
}

server {
    listen 443 ssl http2;
    server_name devn8n.altios.ai;

    ssl_certificate /etc/nginx/ssl/altios.chained.crt;
    ssl_certificate_key /etc/nginx/ssl/server.key;

    ssl_protocols TLSv1.2 TLSv1.3;
    ssl_ciphers HIGH:!aNULL:!MD5;

    location / {
        proxy_pass         http://127.0.0.1:5678;
        proxy_http_version 1.1;

        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;

        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';

    }
}
